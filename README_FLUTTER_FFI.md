# Image Blend FFI - Flutter 使用指南

这是一个高性能的图像混合 Flutter FFI 库，提供图像叠加、平铺、裁剪等功能。

## 功能特性

- ? **图像叠加**: 支持多种混合模式和蒙版
- ? **图像平铺**: 支持自定义倍数的图像平铺
- ? **图像裁剪**: 自动裁剪图像透明边缘
- ? **缓存管理**: 智能缓存系统提升性能
- ? **异步处理**: 支持 compute 和 Isolate 两种异步方式
- ? **跨平台**: 支持 Android 和 iOS

## 安装

### 1. 添加依赖

在 `pubspec.yaml` 中添加：

```yaml
dependencies:
  image_blend_ffi: ^1.0.0
  ffi: ^2.1.0
```

### 2. 复制原生库文件

#### Android

将构建好的 `.so` 文件复制到 Flutter 项目：

```bash
cp -r android/libs/* your_flutter_project/android/src/main/jniLibs/
```

在 `android/app/build.gradle` 中添加：

```gradle
android {
    sourceSets {
        main {
            jniLibs.srcDirs = ['src/main/jniLibs']
        }
    }
}
```

#### iOS

将构建好的 `.framework` 文件复制到 Flutter 项目：

```bash
cp -r ios/frameworks/image_blend_ffi.framework your_flutter_project/ios/Frameworks/
```

在 Xcode 中：

1. 打开 `ios/Runner.xcworkspace`
2. 将 `image_blend_ffi.framework` 拖入项目
3. 在 "Frameworks, Libraries, and Embedded Content" 中设置为 "Embed & Sign"

## 基本使用

### 导入库

```dart
import 'package:image_blend_ffi/image_blend.dart';
```

### 图像叠加

#### 基本叠加

```dart
// 使用 compute (推荐用于 UI 线程)
final result = await overlayImages(
  baseImagePath: '/path/to/base.png',
  topImagePath: '/path/to/top.png',
  outputPath: '/path/to/output.png',
  blendMode: BlendMode.normal,
  opacity: 75,
);

if (result.isSuccess) {
  print('图像叠加成功');
} else {
  print('错误: ${result.errorMessage}');
}
```

#### 使用蒙版

```dart
final result = await overlayImages(
  baseImagePath: '/path/to/base.png',
  topImagePath: '/path/to/top.png',
  outputPath: '/path/to/output.png',
  blendMode: BlendMode.multiply,
  opacity: 50,
  maskImagePath: '/path/to/mask.png', // 蒙版图像
);
```

### 图像平铺

```dart
final result = await tileImage(
  imagePath: '/path/to/image.png',
  outputPath: '/path/to/tiled_output.png',
  tileMultiplier: 3, // 3x3 平铺
);
```

### 图像裁剪

```dart
final result = await trimImage(
  inputPath: '/path/to/input.png',
  outputPath: '/path/to/trimmed_output.png',
);
```

### 清理缓存

```dart
final result = await clearCache();
```

## 高级使用

### 批量处理

```dart
final paramsList = [
  OverlayImageParams(
    baseImagePath: '/path/to/base1.png',
    topImagePath: '/path/to/top1.png',
    outputPath: '/path/to/output1.png',
  ),
  OverlayImageParams(
    baseImagePath: '/path/to/base2.png',
    topImagePath: '/path/to/top2.png',
    outputPath: '/path/to/output2.png',
  ),
];

final results = await batchOverlayImages(paramsList);
for (int i = 0; i < results.length; i++) {
  if (results[i].isSuccess) {
    print('图像 ${i + 1} 处理成功');
  } else {
    print('图像 ${i + 1} 处理失败: ${results[i].errorMessage}');
  }
}
```

### 使用服务类

```dart
final imageBlendService = ImageBlendService();

// 使用服务类的方法
final result = await imageBlendService.overlayImages(
  baseImagePath: '/path/to/base.png',
  topImagePath: '/path/to/top.png',
  outputPath: '/path/to/output.png',
);
```

## 混合模式

```dart
enum BlendMode {
  normal,     // 正常
  multiply,   // 正片叠底
  overlay,    // 叠加
  softLight,  // 柔光
  screen,     // 滤色
}
```

## 平铺模式

```dart
enum TilingMode {
  stretch,    // 拉伸
  tile,       // 平铺
}
```

## 错误处理

```dart
final result = await overlayImages(
  baseImagePath: '/path/to/base.png',
  topImagePath: '/path/to/top.png',
  outputPath: '/path/to/output.png',
);

if (!result.isSuccess) {
  switch (result.errorCode) {
    case FFIErrorCodes.fileNotFound:
      print('文件未找到');
      break;
    case FFIErrorCodes.invalidParams:
      print('参数无效');
      break;
    case FFIErrorCodes.processingFailed:
      print('处理失败');
      break;
    case FFIErrorCodes.outOfMemory:
      print('内存不足');
      break;
    default:
      print('未知错误: ${result.errorMessage}');
  }
}
```

## 性能优化建议

### 1. 异步处理

所有图像处理操作都在 Isolate 中执行，提供完全隔离的后台处理，不会阻塞 UI 线程：

```dart
// 所有操作都使用 Isolate 进行异步处理
final result = await overlayImages(...);
```

### 2. 缓存管理

定期清理缓存以释放内存：

```dart
// 在适当的时机清理缓存
await clearCache();
```

### 3. 批量处理

对于多个图像的处理，使用批量处理可以提高效率：

```dart
// 批量处理比逐个处理更高效
final results = await batchOverlayImages(paramsList);
```

## 示例应用

查看 `example/` 目录中的完整示例应用，了解如何在实际项目中使用这个库。

```bash
cd example
flutter run
```

## 故障排除

### 1. 库加载失败

确保原生库文件已正确复制到项目中：

- Android: 检查 `android/src/main/jniLibs/` 目录
- iOS: 检查 Framework 是否正确嵌入

### 2. 文件路径问题

确保使用绝对路径，并且文件确实存在：

```dart
import 'dart:io';

final file = File('/path/to/image.png');
if (await file.exists()) {
  // 文件存在，可以处理
} else {
  // 文件不存在
}
```

### 3. 权限问题

确保应用有读写文件的权限：

- Android: 在 `android/app/src/main/AndroidManifest.xml` 中添加权限
- iOS: 在 `ios/Runner/Info.plist` 中添加权限

## 更多信息

- [构建指南](BUILD_GUIDE.md)
- [FFI 文档](README_FFI.md)
- [转换总结](CONVERSION_SUMMARY.md)

## 许可证

MIT License
