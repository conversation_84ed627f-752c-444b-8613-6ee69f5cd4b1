/// 图像混合服务 - 高级封装和便捷方法

import 'dart:io';
import '../image_blend_ffi.dart';
import '../models/blend_models.dart';

/// 图像混合服务类
class ImageBlendService {
  static ImageBlendService? _instance;

  /// 单例模式
  factory ImageBlendService() {
    return _instance ??= ImageBlendService._internal();
  }

  ImageBlendService._internal();

  /// 使用 Isolate 执行图像叠加
  Future<FFIResult> overlayImages({
    required String baseImagePath,
    required String topImagePath,
    required String outputPath,
    BlendMode blendMode = BlendMode.normal,
    TilingMode tilingMode = TilingMode.stretch,
    int opacity = 100,
    int tilingScale = 100,
    String? maskImagePath,
  }) async {
    final params = OverlayImageParams(
      baseImagePath: baseImagePath,
      topImagePath: topImagePath,
      outputPath: outputPath,
      blendMode: blendMode,
      tilingMode: tilingMode,
      opacity: opacity,
      tilingScale: tilingScale,
      maskImagePath: maskImagePath,
    );

    // 验证文件是否存在
    final validationResult = await _validateInputFiles([
      baseImagePath,
      topImagePath,
      if (maskImagePath != null) maskImagePath,
    ]);

    if (!validationResult.isSuccess) {
      return validationResult;
    }

    return await ImageBlendAsyncHelper.overlayImagesAsync(params);
  }

  /// 使用 Isolate 执行图像平铺
  Future<FFIResult> tileImage({
    required String imagePath,
    required String outputPath,
    int tileMultiplier = 2,
  }) async {
    final params = TileImageParams(
      imagePath: imagePath,
      outputPath: outputPath,
      tileMultiplier: tileMultiplier,
    );

    // 验证文件是否存在
    final validationResult = await _validateInputFiles([imagePath]);
    if (!validationResult.isSuccess) {
      return validationResult;
    }

    return await ImageBlendAsyncHelper.tileImageAsync(params);
  }

  /// 使用 Isolate 执行图像裁剪
  Future<FFIResult> trimImage({
    required String inputPath,
    required String outputPath,
  }) async {
    final params = TrimImageParams(
      inputPath: inputPath,
      outputPath: outputPath,
    );

    // 验证文件是否存在
    final validationResult = await _validateInputFiles([inputPath]);

    if (!validationResult.isSuccess) {
      return validationResult;
    }

    return await ImageBlendAsyncHelper.trimImageAsync(params);
  }

  /// 使用 Isolate 清理缓存
  Future<FFIResult> clearCache() async {
    return await ImageBlendAsyncHelper.clearCacheAsync();
  }

  /// 批量处理图像叠加
  Future<List<FFIResult>> batchOverlayImages(
    List<OverlayImageParams> paramsList,
  ) async {
    final results = <FFIResult>[];

    for (final params in paramsList) {
      final result = await ImageBlendAsyncHelper.overlayImagesAsync(params);
      results.add(result);
    }

    return results;
  }

  /// 验证输入文件是否存在
  Future<FFIResult> _validateInputFiles(List<String> filePaths) async {
    for (final path in filePaths) {
      final file = File(path);
      if (!await file.exists()) {
        return FFIResult.error(FFIErrorCodes.fileNotFound, '文件不存在: $path');
      }
    }
    return FFIResult.success();
  }
}
