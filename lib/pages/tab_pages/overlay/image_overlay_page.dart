import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:nanami_flutter/common/controllers/image_overlay_controller.dart';
import 'package:nanami_flutter/common/controllers/setting_controller.dart';
import 'package:nanami_flutter/common/theme/color.dart';
import 'package:nanami_flutter/common/utils/logger.dart';
import 'package:nanami_flutter/pages/tab_pages/overlay/sheets/overlay_parameters_sheet.dart';
import 'package:nanami_flutter/pages/tab_pages/overlay/sheets/overlay_settings_sheet.dart';
import 'package:nanami_flutter/widgets/bottom_actions.dart';
import 'package:nanami_flutter/widgets/face_detect_setting_sheet.dart';
import 'package:nanami_flutter/widgets/github_action_item.dart';
import 'package:nanami_flutter/widgets/overlay_image_tile_item_back.dart';
import 'package:nanami_flutter/widgets/overlay_images_preview.dart';
import 'package:nanami_flutter/widgets/overlay_watermark_tile_item.dart';
import 'package:nanami_flutter/widgets/show_mask_setting_sheet.dart';
import 'package:pull_down_button/pull_down_button.dart';
import 'package:super_cupertino_navigation_bar/super_cupertino_navigation_bar.dart';
import 'package:loader_overlay/loader_overlay.dart';

class ImageOverlayPage extends StatefulWidget {
  const ImageOverlayPage({super.key});

  @override
  State<ImageOverlayPage> createState() => _ImageOverlayPageState();
}

class _ImageOverlayPageState extends State<ImageOverlayPage>
    with SingleTickerProviderStateMixin {
  // 获取控制器
  final ImageOverlayController controller = Get.find<ImageOverlayController>();

  // 设置 Controller
  final SettingController _settingController = Get.find<SettingController>();

  // logger
  final logger = LoggerUtil.logger;

  // 模糊效果动画控制器
  late AnimationController _blurAnimationController;
  late Animation<double> _blurAnimation;
  // 添加不透明度动画
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initBlurAnimation();
  }

  @override
  void dispose() {
    _blurAnimationController.dispose();
    super.dispose();
  }

  // 初始化模糊动画控制器
  void _initBlurAnimation() {
    _blurAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 350),
    );
    _blurAnimation = Tween<double>(begin: 0.0, end: 8.0).animate(
      CurvedAnimation(
        parent: _blurAnimationController,
        curve: Curves.easeInOut,
      ),
    );
    // 添加不透明度动画
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _blurAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  // 分段控件映射
  Map<int, Widget> get _segmentChildren {
    final int baseImgtotalCount = controller.overlayImages.length;
    final int baseImgselectedCount = controller.selectedImages.length;

    // 水印总数
    final int watermarkCount = controller.overlayWatermarks.length;

    String baseImgText = '底图';
    if (baseImgtotalCount > 0) {
      // 如果选中的数量与总数量一致，只显示总数量
      if (baseImgselectedCount == baseImgtotalCount) {
        baseImgText = '底图 ($baseImgtotalCount)';
      } else {
        // 否则显示选中数量/总数量
        baseImgText = '底图 ($baseImgselectedCount/$baseImgtotalCount)';
      }
    }

    String watermarkText = '水印';
    if (watermarkCount > 0) {
      watermarkText = '水印 ($watermarkCount)';
    }

    return {
      0: Padding(
        padding: EdgeInsets.symmetric(vertical: 5),
        child: Text(baseImgText),
      ),
      1: Padding(
        padding: EdgeInsets.symmetric(vertical: 5),
        child: Text(watermarkText),
      ),
    };
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: CupertinoColors.systemBackground,
      child: SuperScaffold(
        scrollController: controller.scrollController,
        appBar: SuperAppBar(
          height: 44,
          backgroundColor: MyBgColor.appBarBgColor(context),
          searchBar: SuperSearchBar(enabled: false),
          largeTitle: SuperLargeTitle(largeTitle: "贴膜"),
          // 右边下拉菜单
          actions: Padding(
            padding: const EdgeInsets.only(right: 6.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Obx(
                  () =>
                      controller.selectedSegment.value == 0
                          ? CupertinoButton(
                            onPressed: () {
                              showShowMaskSettingSheet(context);
                            },
                            padding: EdgeInsets.zero,
                            child: const Icon(CupertinoIcons.eye, size: 24.0),
                          )
                          : const SizedBox.shrink(),
                ),
                PullDownButton(
                  itemBuilder: (context) {
                    return _buildPullDownMenuItem(context);
                  },
                  buttonBuilder: (context, showMenu) {
                    return CupertinoButton(
                      onPressed: showMenu,
                      padding: EdgeInsets.zero,
                      child: const Icon(
                        CupertinoIcons.ellipsis_circle,
                        size: 24.0,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          // 导航栏底部区域
          bottom: SuperAppBarBottom(
            height: 83,
            enabled: true,
            child: Obx(
              () => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // 视图控制器
                    CupertinoSlidingSegmentedControl<int>(
                      groupValue: controller.selectedSegment.value,
                      onValueChanged: controller.changeSegment,
                      children: _segmentChildren,
                    ),
                    // 底部按钮
                    _buildBottomActions(context),
                  ],
                ),
              ),
            ),
          ),
        ),
        body: Obx(
          () => IndexedStack(
            index: controller.selectedSegment.value,
            children: [
              // 底图 tab 内容
              _buildBaseImgTabContent(context),
              // 水印 tab 内容
              _buildWatermarkTabContent(context),
            ],
          ),
        ),
      ),
    );
  }

  // 构建脸部识别加载效果
  Widget _overlayWidgetforFaceDetectBuilder(
    BuildContext context,
    dynamic process,
  ) {
    // 启动模糊动画
    _blurAnimationController.forward();

    return AnimatedBuilder(
      animation: _blurAnimationController,
      builder: (context, child) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: _blurAnimation.value,
            sigmaY: _blurAnimation.value,
            tileMode: TileMode.clamp,
          ),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Lottie 动画
                  Lottie.asset(
                    "assets/lotties/face_detecing.json",
                    width: 200,
                    height: 200,
                    frameRate: FrameRate.max,
                  ),
                  // const SizedBox(height: 16),
                  Text(
                    "正在识别脸部",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: CupertinoColors.systemBlue.resolveFrom(context),
                    ),
                  ),
                  const SizedBox(height: 8),
                  // 百分比
                  Obx(
                    () => Text(
                      "${controller.batchDetectProgressPercent.value.toStringAsFixed(0)} %",
                      style: TextStyle(
                        fontSize: 14,
                        color: CupertinoColors.secondaryLabel.resolveFrom(
                          context,
                        ),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // 构建贴膜加载效果
  Widget _overlayWidgetforOverlayBuilder(
    BuildContext context,
    dynamic process,
  ) {
    // 启动模糊动画
    _blurAnimationController.forward();

    return AnimatedBuilder(
      animation: _blurAnimationController,
      builder: (context, child) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: _blurAnimation.value,
            sigmaY: _blurAnimation.value,
            tileMode: TileMode.clamp,
          ),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Lottie 动画
                  ColorFiltered(
                    colorFilter: const ColorFilter.mode(
                      CupertinoColors.systemBlue,
                      BlendMode.srcATop,
                    ),
                    child: Lottie.asset(
                      "assets/lotties/ai-image.json",
                      width: 210,
                      height: 210,
                      frameRate: FrameRate.max,
                    ),
                  ),
                  // const SizedBox(height: 16),
                  Text(
                    "正在贴膜",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: CupertinoColors.systemBlue.resolveFrom(context),
                    ),
                  ),
                  const SizedBox(height: 8),
                  // 百分比
                  Obx(
                    () => Text(
                      "${controller.overlayProgressPercent.value.toStringAsFixed(0)} %",
                      style: TextStyle(
                        fontSize: 14,
                        color: CupertinoColors.secondaryLabel.resolveFrom(
                          context,
                        ),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // 构建底图 tab 内容
  Widget _buildBaseImgTabContent(BuildContext context) {
    return Obx(() {
      // 有底图的时候，渲染列表
      if (controller.overlayImages.isNotEmpty) {
        return _buildOverlayImagesList(context);
      } else {
        // 空底图提示
        return Center(
          child: Text(
            '未添加底图',
            style: TextStyle(
              color: CupertinoColors.tertiaryLabel.resolveFrom(context),
            ),
          ),
        );
      }
    });
  }

  // 构建底图列表
  Widget _buildOverlayImagesList(BuildContext context) {
    // 获取媒体查询的底部内边距
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    return AnimatedList(
      key: controller.baseImgListKey,
      padding: EdgeInsets.only(bottom: bottomPadding),
      initialItemCount: controller.overlayImages.length,
      itemBuilder: (context, index, animation) {
        // 获取底图对象
        OverlayImage overlayImage = controller.overlayImages[index];
        return _buildAnimatedItem(overlayImage, context, animation, index);
      },
    );
  }

  // 构建底图列表项（带有动画）
  Widget _buildAnimatedItem(
    OverlayImage overlayImage,
    BuildContext context,
    Animation<double> animation,
    int index,
  ) {
    // 使用FadeTransition和SizeTransition组合动画效果
    return SizeTransition(
      sizeFactor: animation,
      child: FadeTransition(
        opacity: animation,
        child: Column(
          children: [
            // 使用Obx包装OverlayImageTileItem，以便在overlayImage属性变化时自动刷新UI
            Obx(() {
              return OverlayImageTileItemLeft(
                key: ValueKey(overlayImage.filePath), // 使用文件路径作为唯一key
                title: overlayImage.name,
                imagePath: overlayImage.filePath,
                maskPath: overlayImage.maskFilePath.value,
                maskBlendMode: _settingController.showMaskBlendMode.value,
                maskOpacity: _settingController.showMaskOpacity.value,
                checked: overlayImage.checked.value,
                // 图片点击进入预览页
                onImageTap: () {
                  Get.to(
                    () => OverlayImagesPreview(
                      overlayImages: controller.overlayImages,
                      initIndex: index,
                    ),
                  );
                },
                // 触发删除
                onDelete: () {
                  // 调用控制器的移除方法
                  controller.removeOverlayImageByIndex(index);
                },
                // 选择状态改变
                onChanged: (value) {
                  if (value != null) {
                    overlayImage.checked.value = value;
                  }
                },
                // 识别真人脸部
                onDetectHumanFace: () {
                  _detectHumanFace(context, overlayImage);
                },
                // 识别动漫脸部（暂未实现）
                onDetectAnimeFace: () {
                  // TODO: 实现动漫脸识别功能
                },
                // 手动标注（暂未实现）
                onManualMark: () {
                  // TODO: 实现手动标注功能
                },
              );
            }),
            // 分割线
            Divider(
              height: 1,
              color: CupertinoColors.systemGroupedBackground.resolveFrom(
                context,
              ),
              indent: 97.0,
              endIndent: 15.0,
            ),
          ],
        ),
      ),
    );
  }

  // 构建水印 tab 内容
  Widget _buildWatermarkTabContent(BuildContext context) {
    return Obx(() {
      // 有水印的时候，渲染列表
      if (controller.overlayWatermarks.isNotEmpty) {
        return _buildOverlayWatermarksList(context);
      } else {
        // 空水印提示
        return Center(
          child: Text(
            '未添加水印',
            style: TextStyle(
              color: CupertinoColors.tertiaryLabel.resolveFrom(context),
            ),
          ),
        );
      }
    });
  }

  // 构建水印列表
  Widget _buildOverlayWatermarksList(BuildContext context) {
    // 获取媒体查询的底部内边距
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    return AnimatedList(
      key: controller.watermarkListKey,
      padding: EdgeInsets.only(bottom: bottomPadding),
      initialItemCount: controller.overlayWatermarks.length,
      itemBuilder: (context, index, animation) {
        // 获取水印对象
        OverlayWatermark overlayWatermark = controller.overlayWatermarks[index];
        return _buildAnimatedWatermarkItem(
          overlayWatermark,
          context,
          animation,
          index,
        );
      },
    );
  }

  // 构建水印列表项（带有动画）
  Widget _buildAnimatedWatermarkItem(
    OverlayWatermark overlayWatermark,
    BuildContext context,
    Animation<double> animation,
    int index,
  ) {
    // 使用FadeTransition和SizeTransition组合动画效果
    return SizeTransition(
      sizeFactor: animation,
      child: FadeTransition(
        opacity: animation,
        child: Column(
          children: [
            // 使用Obx包装OverlayWatermarkTileItem，以便在overlayWatermark属性变化时自动刷新UI
            Obx(() {
              return OverlayWatermarkTileItem(
                key: ValueKey(overlayWatermark.filePath.value), // 使用文件路径作为唯一key
                title: overlayWatermark.name.value,
                imagePath: overlayWatermark.filePath.value,
                // 图片点击进入预览页（暂未实现）
                onImageTap: () {
                  // 预览功能暂未实现
                },
                onRename: () {
                  _showRenameWatermarkDialog(context, overlayWatermark, index);
                },
                onTitleLongPressed: () {
                  // 振动反馈
                  HapticFeedback.vibrate();
                  // 弹出重命名模态框
                  _showRenameWatermarkDialog(context, overlayWatermark, index);
                },
                // 触发删除
                onDelete: () {
                  // 调用控制器的移除方法
                  controller.removeOverlayWatermarkByIndex(index);
                },
              );
            }),
            // 分割线
            Divider(
              height: 1,
              color: CupertinoColors.systemGroupedBackground.resolveFrom(
                context,
              ),
              indent: 97.0,
              endIndent: 15.0,
            ),
          ],
        ),
      ),
    );
  }

  // 构建底部按钮
  Widget _buildBottomActions(BuildContext context) {
    return // 如果当前在底图 tab 则显示底图相关按钮
    Obx(
      () =>
          controller.selectedSegment.value == 0
              ? BottomActions(
                verticalPadding: 8,
                horizontalPadding: 0,
                actions: [
                  GithubActionItem(
                    icon: CupertinoIcons.viewfinder,
                    text: '真人',
                    onPressed: () {
                      _detectAllHumanFaces(context);
                    },
                  ),
                  GithubActionItem(
                    icon: CupertinoIcons.viewfinder,
                    text: '动漫',
                    onPressed: () {},
                  ),
                  Obx(
                    () => GithubActionItem(
                      text:
                          '${_settingController.faceDetectExpansionRate.value} %',
                      onPressed: () {
                        // 弹出范围扩展倍率选择 sheet
                        showFaceDetectSettingSheet(context);
                      },
                      showDropdownArrow: true,
                    ),
                  ),
                  GithubActionItem(
                    icon: CupertinoIcons.delete,
                    iconColor: CupertinoColors.destructiveRed,
                    textColor: CupertinoColors.destructiveRed,
                    text: '清空',
                    onPressed: () {
                      _showClearConfirmDialog(context);
                    },
                  ),
                ],
              )
              // 如果当前在水印 tab 则显示水印相关按钮
              : BottomActions(
                verticalPadding: 8,
                horizontalPadding: 0,
                actions: [
                  GithubActionItem(
                    icon: CupertinoIcons.delete,
                    iconColor: CupertinoColors.destructiveRed,
                    textColor: CupertinoColors.destructiveRed,
                    text: '清空',
                    onPressed: () {
                      _showClearWatermarksConfirmDialog(context);
                    },
                  ),
                ],
              ),
    );
  }

  // 识别真人脸部(单张图片)，调用控制器方法并处理动画
  void _detectHumanFace(BuildContext context, OverlayImage overlayImage) async {
    _blurAnimationController.reset();
    context.loaderOverlay.show();

    try {
      await controller.detectHumanFace(context, overlayImage);
    } finally {
      // 反向运行模糊动画
      _blurAnimationController.reverse().then((_) {
        if (context.mounted) {
          context.loaderOverlay.hide();
        }
      });
    }
  }

  // 批量识别所有图片中的人脸，调用控制器方法并处理动画
  void _detectAllHumanFaces(BuildContext context) async {
    // 重置动画控制器
    _blurAnimationController.reset();

    // 显示加载层
    context.loaderOverlay.show(
      widgetBuilder:
          (progress) => _overlayWidgetforFaceDetectBuilder(context, progress),
    );

    try {
      await controller.detectAllHumanFaces();
    } finally {
      // 反向运行模糊动画
      _blurAnimationController.reverse().then((_) {
        if (context.mounted) {
          context.loaderOverlay.hide();
        }
      });
    }
  }

  // 开始贴膜，调用控制器方法并处理动画
  void _startOverlay(BuildContext context) async {
    // 重置动画控制器
    _blurAnimationController.reset();

    // 显示加载层
    context.loaderOverlay.show(
      widgetBuilder:
          (progress) => _overlayWidgetforOverlayBuilder(context, progress),
    );

    try {
      await controller.startOverlay(context);
    } finally {
      // 反向运行模糊动画
      _blurAnimationController.reverse().then((_) {
        if (context.mounted) {
          context.loaderOverlay.hide();
        }
      });
    }
  }

  // 显示清空底图确认对话框
  void _showClearConfirmDialog(BuildContext context) {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return CupertinoAlertDialog(
          title: const Text('清空底图列表？'),
          content: Text("将会移除所有底图。"),
          actions: [
            CupertinoDialogAction(
              child: const Text('取消'),
              onPressed: () {
                Navigator.pop(dialogContext);
              },
            ),
            CupertinoDialogAction(
              isDestructiveAction: true,
              child: const Text('清空'),
              onPressed: () {
                // 调用控制器的清空方法
                controller.clearOverlayImages();
                Navigator.pop(dialogContext);
              },
            ),
          ],
        );
      },
    );
  }

  // 显示清空水印确认对话框
  void _showClearWatermarksConfirmDialog(BuildContext context) {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return CupertinoAlertDialog(
          title: const Text('清空水印列表？'),
          content: Text("将会移除所有水印。"),
          actions: [
            CupertinoDialogAction(
              child: const Text('取消'),
              onPressed: () {
                Navigator.pop(dialogContext);
              },
            ),
            CupertinoDialogAction(
              isDestructiveAction: true,
              child: const Text('清空'),
              onPressed: () {
                // 调用控制器的清空方法
                controller.clearOverlayWatermarks();
                Navigator.pop(dialogContext);
              },
            ),
          ],
        );
      },
    );
  }

  // 显示水印重命名对话框
  void _showRenameWatermarkDialog(
    BuildContext context,
    OverlayWatermark overlayWatermark,
    int index,
  ) {
    // 创建一个文本控制器，初始值为空字符串
    final TextEditingController textController = TextEditingController();

    // 创建一个焦点节点，用于控制键盘焦点
    final FocusNode focusNode = FocusNode();

    // 显示中间模态对话框
    showCupertinoDialog<void>(
      context: context,
      barrierDismissible: true, // 点击背景可关闭对话框
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: const Text('重命名水印'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 当前名称显示
              Padding(
                padding: const EdgeInsets.only(top: 8.0, bottom: 12.0),
                child: Text(
                  overlayWatermark.name.value,
                  style: TextStyle(
                    color: CupertinoColors.secondaryLabel.resolveFrom(context),
                    fontSize: 13.0,
                  ),
                ),
              ),
              // 输入框
              CupertinoTextField(
                controller: textController,
                focusNode: focusNode,
                placeholder: '输入新名称',
                clearButtonMode: OverlayVisibilityMode.editing,
                decoration: BoxDecoration(
                  color: CupertinoColors.systemFill.resolveFrom(context),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 12.0,
                  vertical: 8.0,
                ),
                autofocus: true,
              ),
            ],
          ),
          actions: [
            // 取消按钮
            CupertinoDialogAction(
              isDefaultAction: true,
              child: const Text('取消'),
              onPressed: () {
                // 首先收起键盘
                focusNode.unfocus();
                // 然后关闭对话框
                Navigator.of(context).pop();
              },
            ),
            // 确认按钮
            CupertinoDialogAction(
              child: const Text('确认'),
              onPressed: () {
                // 首先收起键盘
                focusNode.unfocus();

                // 获取新名称
                final newName = textController.text.trim();
                // 检查名称是否为空
                if (newName.isEmpty) {
                  // 显示错误提示
                  showCupertinoDialog(
                    context: context,
                    builder:
                        (context) => CupertinoAlertDialog(
                          title: const Text('错误'),
                          content: const Text('名称不能为空'),
                          actions: [
                            CupertinoDialogAction(
                              child: const Text('好'),
                              onPressed: () => Navigator.pop(context),
                            ),
                          ],
                        ),
                  );
                  return;
                }

                // 调用控制器的重命名方法
                controller.renameWatermark(index, newName);

                // 关闭对话框
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    ).then((_) {
      // 释放资源
      textController.dispose();
      focusNode.dispose();
    });
  }

  // 构建下拉菜单 item
  List<PullDownMenuEntry> _buildPullDownMenuItem(BuildContext context) {
    // 底图专属的添加 item
    List<PullDownMenuEntry> baseImgAddItems = [
      PullDownMenuItem(
        title: '从设备添加底图',
        icon: CupertinoIcons.device_phone_portrait,
        onTap: () {
          controller.addBaseImageFromDevice(context);
        },
      ),
      PullDownMenuItem(
        title: '从图库添加底图',
        icon: CupertinoIcons.photo_on_rectangle,
        onTap: () {
          controller.addBaseImageFromGallery(context);
        },
      ),
      PullDownMenuItem(
        title: '从相簿添加底图',
        icon: CupertinoIcons.rectangle_stack,
        onTap: () {
          controller.addBaseImageFromImageGroup(context);
        },
      ),
      PullDownMenuDivider.large(),
    ];
    // 水印专属的添加 item
    List<PullDownMenuEntry> watermarkAddItems = [
      PullDownMenuItem(
        title: '从设备添加水印',
        icon: CupertinoIcons.device_phone_portrait,
        onTap: () {
          controller.addWatermarkFromDevice(context);
        },
      ),
      PullDownMenuDivider.large(),
    ];
    // 公共的 item
    List<PullDownMenuEntry> commonItems = [
      PullDownMenuItem(
        title: '输出设置',
        icon: CupertinoIcons.settings,
        onTap: () {
          showOverlaySettingsSheet(context);
        },
      ),
      PullDownMenuItem(
        title: '贴膜参数',
        icon: CupertinoIcons.slider_horizontal_3,
        onTap: () {
          showOverlayParametersSheet(context);
        },
      ),
      PullDownMenuDivider.large(),
      PullDownMenuItem(
        title: '打开输出目录',
        icon: CupertinoIcons.folder,
        onTap: () {
          controller.openOutputDir();
        },
      ),
      PullDownMenuDivider.large(),
      PullDownMenuItem(
        title: '快速贴膜',
        icon: CupertinoIcons.bolt_fill,
        enabled: controller.overlayImages.isNotEmpty,
        onTap: () {},
      ),
      PullDownMenuItem(
        title: '开始贴膜',
        icon: CupertinoIcons.play_fill,
        enabled:
            controller.overlayImages.isNotEmpty &&
            controller.overlayWatermarks.isNotEmpty,
        onTap: () {
          _startOverlay(context);
        },
      ),
    ];
    // 根据当前选中的 tab 返回对应的 item
    return controller.selectedSegment.value == 0
        ? [...baseImgAddItems, ...commonItems]
        : [...watermarkAddItems, ...commonItems];
  }
}
